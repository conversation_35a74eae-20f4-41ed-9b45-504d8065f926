<?php
// api/submit.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

$firstName = $input['firstName'] ?? '';
$lastName = $input['lastName'] ?? '';
$email = $input['email'] ?? '';
$phone = $input['phone'] ?? '';
$message = $input['message'] ?? '';
$language = $input['language'] ?? 'en';

// Database connection
$host = '**************';
$dbname = 'dbgklrx5bfi6jr';
$username = 'ugs8rbuojyel6';
$password = 'Genpack4007!';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Insert submission
    $stmt = $pdo->prepare("INSERT INTO submissions (first_name, last_name, email, phone, message, language) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$firstName, $lastName, $email, $phone, $message, $language]);
    
    // Send email
    $to = '<EMAIL>';
    $subject = $language === 'es' ? 'Nueva Consulta - Tessie Santiago' : 'New Submission - Tessie Santiago';
    $emailBody = "Name: $firstName $lastName\nEmail: $email\nPhone: $phone\nMessage: $message\nLanguage: $language";
    
    mail($to, $subject, $emailBody);
    
    echo json_encode([
        'success' => true,
        'message' => $language === 'es' ? 'Gracias — recibido.' : 'Thanks — we got it.'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to submit form']);
}
?>
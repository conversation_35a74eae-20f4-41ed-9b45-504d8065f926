// assets/main.js
// <PERSON> toggle (EN/ES). Text map keeps copy tight and consistent.
const map = {
  es: {
    kicker: "Asesoría financiera bilingüe — EN/ES",
    h1: "Financiamiento para Negocios, Testamentos y Fideicomisos, Retiro y Optimización Fiscal",
    sub: "Asesoría confiable para familias y emprendedores. Lista para alianza con Texas Funding Solutions para préstamos comerciales.",
    cta1: "Agenda una llamada gratis de 15 min",
    cta2: "Préstamos para Negocios",
    cta3: "Prensa: Telemundo Semanal",
    band1: "Consejos semanales en Telemundo:",
    band2: "Tessie comparte consejos financieros una vez por semana. Fechas: por confirmar.",
    svcKick: "Servicios",
    svc1: "Asistencia para Préstamos",
    svc1a: "Pre‑calificación",
    svc1b: "Lista de documentos",
    svc1c: "Socia: Texas Funding Solutions",
    svc2: "Planificación Patrimonial",
    svc2a: "Fideicomisos y Testamentos",
    svc2b: "Poderes y HIPAA",
    svc2c: "Transferencia de escritura",
    svc3: "Retiro y Impuestos",
    svc3a: "Estrategias de ingreso de retiro",
    svc3b: "Optimización fiscal",
    svc3c: "Revisiones anuales",
    contactH2: "Planifiquemos tu siguiente paso",
    contactP: "Cuéntanos de ti. Responderemos en un día hábil.",
    send: "Enviar",
    wa: "WhatsApp",
    call: "Llamar",
    zoom: "Agendar Zoom",
    trust: "Por qué trabajar con Tessie",
    t1: "Servicio transparente y bilingüe",
    t2: "Alianzas para resultados reales de financiamiento",
    t3: "Planes claros con revisiones anuales",
    docs: "Paquetes Populares",
    p1: "Paquete Básico de Patrimonio",
    p2: "Fideicomiso Revocable + Testamento Complementario",
    p3: "Poderes + HIPAA + Transferencia"
  }
};
const qs = (s, ctx = document) => ctx.querySelector(s);
const qsa = (s, ctx = document) => [...ctx.querySelectorAll(s)];
function setLang(lang) {
  const es = map.es;
  const useES = lang === 'es';
  qs('html').setAttribute('lang', useES ? 'es' : 'en');
  qs('#langField')?.setAttribute('value', useES ? 'es' : 'en');
  qsa('[data-lang]').forEach(b => b.setAttribute('aria-pressed', b.dataset.lang === lang));
  if (!useES) return; // EN is default, text already in markup
  qsa('[data-i18n]').forEach(el => {
    const key = el.getAttribute('data-i18n');
    if (es[key]) el.textContent = es[key];
  });
}
qsa('[data-lang]').forEach(b => b.addEventListener('click', () => setLang(b.dataset.lang)));
document.getElementById('y')?.textContent = new Date().getFullYear();

// Replace the existing form handler (lines 60-75) with:
const form = document.getElementById('leadForm');
if (form) {
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    const msg = document.getElementById('formMsg');
    const currentLang = document.querySelector('html').getAttribute('lang') || 'en';

    msg.textContent = currentLang === 'es' ? 'Enviando...' : 'Sending...';

    try {
      const formData = new FormData(form);
      const data = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        message: formData.get('message'),
        language: currentLang
      };

      const res = await fetch('http://34.174.199.156:3001/api/submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      const result = await res.json();

      if (result.success) {
        msg.textContent = result.message;
        form.reset();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      console.error('Form submission error:', err);
      msg.textContent = currentLang === 'es'
        ? 'Por favor intenta de nuevo o contáctanos por WhatsApp.'
        : 'Please try again or WhatsApp us.';
    }
  });
}


// Lightweight form UX; expects a Google Apps Script Web App endpoint
/* const form = document.getElementById('leadForm');
if(form){
  form.addEventListener('submit', async (e)=>{
    e.preventDefault();
    const msg = document.getElementById('formMsg');
    msg.textContent = 'Sending...';
    try{
      const fd = new FormData(form);
      const res = await fetch(form.action, { method:'POST', mode:'no-cors', body: fd });
      msg.textContent = 'Thanks — we got it.';
      form.reset();
    }catch(err){
      msg.textContent = 'Please try again or WhatsApp us.';
    }
  });
} */

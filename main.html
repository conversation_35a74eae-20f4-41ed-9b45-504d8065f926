<!-- press.html -->
<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>Press — Telemundo Weekly | Tessie Santiago</title>
<meta name="description" content="Press hub for Tessie Santiago. Telemundo weekly finance tips schedule, media kit, and contact.">
<link rel="icon" href="/assets/favicon.ico">
<meta property="og:title" content="Press — Telemundo Weekly | Tessie Santiago">
<meta property="og:type" content="article">
<style>body{margin:0;font:16px/1.5 system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,'Helvetica Neue',Arial,sans-serif;background:#f7f9fb;color:#0d1b2a}main,header,footer{max-width:1120px;margin:auto;padding:24px}a{color:#0a6cff;text-decoration:none}.pill{border:1px solid #dce5ee;border-radius:999px;padding:8px 14px;background:#fff}.card{background:#fff;border:1px solid #e6edf5;border-radius:14px;padding:16px;margin:12px 0}.grid{display:grid;grid-template-columns:1fr 1fr;gap:16px}@media(max-width:900px){.grid{grid-template-columns:1fr}}</style>
<script type="application/ld+json">{
"@context":"https://schema.org",
"@type":"CollectionPage",
"name":"Press — Telemundo Weekly | Tessie Santiago",
"url":"https://tessiesantiago.info/press",
"hasPart":[{"@type":"Article","headline":"Telemundo Weekly Finance Tips — Dates TBA","author":{"@type":"Person","name":"Tessie Santiago"}}]
}</script>
</head>
<body>
<header>
  <a class="pill" href="/">← Home</a>
</header>
<main>
  <h1>Press</h1>
  <div class="grid">
    <section class="card">
      <h2>Telemundo Weekly</h2>
      <p>Segment: Finance Tips with Tessie Santiago</p>
      <p>Status: Dates TBA (weekly)</p>
      <ul>
        <li>Topics: Small business funding, estate planning, retirement, taxes</li>
        <li>Language: Spanish (with English resources on site)</li>
      </ul>
    </section>
    <section class="card">
      <h2>Media Contact</h2>
      <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <p>WhatsApp: <a href="https://wa.me/YOUR_NUMBER">Message</a></p>
      <p>Download: <a href="#" aria-disabled="true">Media Kit (coming soon)</a></p>
    </section>
  </div>
</main>
<footer>
  <p>© <span id="y"></span> TessieSantiago.info</p>
</footer>
<script>document.getElementById('y').textContent=new Date().getFullYear()</script>
</body>
</html>

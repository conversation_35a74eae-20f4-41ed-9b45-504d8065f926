// backend/server.js
const express = require('express');
const mysql = require('mysql2/promise');
const nodemailer = require('nodemailer');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// MySQL connection
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'dbgklrx5bfi6jr',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

const pool = mysql.createPool(dbConfig);

// Email transporter
const transporter = nodemailer.createTransport({
    service: 'gmail', // or your preferred service
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});

// Submission endpoint
app.post('/api/submissions', async (req, res) => {
    try {
        const { firstName, lastName, email, phone, message, language = 'en' } = req.body;

        // Validate required fields
        if (!firstName || !lastName || !email) {
            return res.status(400).json({
                error: 'First name, last name, and email are required'
            });
        }

        // Insert into database
        const [result] = await pool.execute(
            'INSERT INTO submissions (first_name, last_name, email, phone, message, language) VALUES (?, ?, ?, ?, ?, ?)',
            [firstName, lastName, email, phone, message, language]
        );

        // Send email notification
        const emailSubject = language === 'es'
            ? 'Nueva Consulta - Tessie Santiago'
            : 'New Submission - Tessie Santiago';

        const emailBody = `
      <h2>${language === 'es' ? 'Nueva consulta recibida' : 'New Submission Received'}</h2>
      <p><strong>${language === 'es' ? 'Nombre' : 'Name'}:</strong> ${firstName} ${lastName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>${language === 'es' ? 'Teléfono' : 'Phone'}:</strong> ${phone || 'Not provided'}</p>
      <p><strong>${language === 'es' ? 'Mensaje' : 'Message'}:</strong> ${message || 'No message'}</p>
      <p><strong>${language === 'es' ? 'Idioma' : 'Language'}:</strong> ${language.toUpperCase()}</p>
      <p><strong>${language === 'es' ? 'Enviado' : 'Submitted'}:</strong> ${new Date().toLocaleString()}</p>
    `;

        await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: '<EMAIL>',
            subject: emailSubject,
            html: emailBody
        });

        res.json({
            success: true,
            message: language === 'es' ? 'Gracias — recibido.' : 'Thanks — we got it.',
            id: result.insertId
        });

    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({
            error: 'Failed to submit form',
            message: 'Please try again or WhatsApp us.'
        });
    }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});